<script lang="ts" setup>
import { ref, onMounted, watch } from "vue"
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from "element-plus"
import { ArrowLeft, DataAnalysis } from "@element-plus/icons-vue"
import type { PatientInfo } from "../management/types"
import { getPatientInfoApi } from "@/common/apis/patients"
import { transformPatientData } from "../management/utils/dataTransform"

defineOptions({
  name: "ImageAnalysis"
})

const route = useRoute()
const router = useRouter()

// 患者详情数据
const patientDetail = ref<PatientInfo | null>(null)
const loading = ref(false)

// 获取患者ID
const patientId = route.params.patientId as string

// 图像数据
const imageData = ref<any>(null)
const imageUrl = ref('')

// 报告模板选项
const reportTemplates = ref([
  { value: 'cardiac', label: '心脏超声检查报告模板' },
  { value: 'abdominal', label: '腹部超声检查报告模板' },
  { value: 'thyroid', label: '甲状腺超声检查报告模板' },
  { value: 'obstetric', label: '产科超声检查报告模板' }
])

const selectedTemplate = ref('cardiac')

// 报告内容
const reportContent = ref('')

// 分析状态
const isAnalyzing = ref(false)

// 聊天相关
const chatInput = ref('')
const chatMessages = ref([
  {
    id: 1,
    type: 'system',
    content: '请对此超声图像进行分析',
    timestamp: new Date()
  }
])



// 性别选项
const genderOptions = ref([
  { value: '男', label: '男' },
  { value: '女', label: '女' }
])

// 获取患者详情数据
const fetchPatientDetail = async () => {
  if (!patientId) {
    ElMessage.error('患者ID不能为空')
    return
  }

  loading.value = true
  try {
    const response = await getPatientInfoApi(patientId)
    if (response.data) {
      const transformedData = transformPatientData(response.data)
      // 设置默认值
      patientDetail.value = {
        ...transformedData,
        name: transformedData.name || '张三',
        gender: transformedData.gender || '男',
        age: transformedData.age || '35',
        id: transformedData.id || 'P001',
        caseNumber: transformedData.caseNumber || 'C001',
        department: transformedData.department || '心内科',
        doctor: transformedData.doctor || '李医生',
        bedNumber: transformedData.bedNumber || '101',
        examPart: transformedData.examPart || '心脏超声检查'
      }
    } else {
      ElMessage.error('获取患者详情失败')
    }
  } catch (error) {
    console.error('获取患者详情失败:', error)
    ElMessage.error('获取患者详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 返回患者详情页面
const goBack = () => {
  router.push({
    name: 'PatientDetail',
    params: { id: patientId }
  })
}

// 自动分析
const autoAnalyze = () => {
  if (isAnalyzing.value) {
    return
  }

  isAnalyzing.value = true
  reportContent.value = ''
  ElMessage.info('正在进行AI自动分析...')

  // 模拟流式输出的报告内容
  const fullReport = `心脏彩超检查报告

1.心脏形态：
  1.左心房（LA）：心房内径正常，房壁回声均匀，未见明显异常回声团块。
  2.右心房（RA）：房腔大小正常，房壁回声正常，未见异常回声。
  3.左心室（LV）：室壁厚度正常，室壁运动正常，未见室壁运动异常。
  4.右心室（RV）：大小及形态正常，室壁回声正常，未见明显异常改变。

2.心脏功能：
  1.左室功能（AO）：测量左室内径，大小正常（测量值未见明显异常）。
  2.左房功能：房室瓣开放正常，房室瓣关闭正常，未见明显反流。

3.心脏瓣膜：
  1.二尖瓣：瓣膜形态正常，开放及关闭功能正常，未见明显狭窄或关闭不全。
  2.三尖瓣：心房室瓣形态正常，瓣膜开放及关闭正常，未见明显异常。

超声影像学检查：上述检查未见，心脏形态、大小、心室壁运动学检查，
基本一致检查。

彩超检查结果：（彩超检查结果，含有彩色血流及心脏）。

彩超检查结果：各检查结果及心脏功能均为正常。

注意事项：
建议定期复查彩超检查，如有不适症状，请及时就诊。

检查医师：
主治医师或主任医师签字，并注明检查时间（如检查时间、心率等）。

心电图（MRI）：彩超检查结果正常，建议定期复查（如检查时间、心率等）。

注意事项：
建议定期复查彩超检查，如有不适症状，请及时就诊。

检查医师或主任医师签字，并注明检查时间。

检查医师或主任医师签字，并注明检查时间（如检查时间、心率等）。

主治医师或主任医师签字，并注明检查时间（如检查时间、心率等）。`

  // 流式输出实现
  let currentIndex = 0
  const streamInterval = setInterval(() => {
    if (currentIndex < fullReport.length) {
      // 每次添加1-3个字符，模拟真实的流式输出
      const chunkSize = Math.floor(Math.random() * 3) + 1
      const chunk = fullReport.slice(currentIndex, currentIndex + chunkSize)
      reportContent.value += chunk
      currentIndex += chunkSize
    } else {
      clearInterval(streamInterval)
      isAnalyzing.value = false
      ElMessage.success('AI分析完成！')
    }
  }, 50) // 每50ms输出一次
}

// 发送聊天消息
const sendMessage = () => {
  if (!chatInput.value.trim()) {
    return
  }

  // 添加用户消息
  chatMessages.value.push({
    id: Date.now(),
    type: 'user',
    content: chatInput.value,
    timestamp: new Date()
  })

  const userMessage = chatInput.value
  chatInput.value = ''

  // 模拟AI回复
  setTimeout(() => {
    chatMessages.value.push({
      id: Date.now() + 1,
      type: 'assistant',
      content: `针对您的问题"${userMessage}"，我正在分析超声图像中的相关信息。根据当前图像显示，可以观察到心脏结构清晰，各房室比例正常。建议结合临床症状进行综合判断。`,
      timestamp: new Date()
    })
  }, 1000)
}

// 图像加载错误处理
const handleImageError = () => {
  ElMessage.error('图像加载失败')
  imageUrl.value = ''
}

// 初始化图像数据
const initImageData = () => {
  const query = route.query
  if (query.imageId) {
    imageData.value = {
      id: query.imageId,
      name: query.imageName || '未知图像',
      size: query.imageSize || '未知大小',
      format: query.imageFormat || '未知格式',
      uploadTime: query.imageUploadTime || '未知时间',
      thumbnail: query.imageThumbnail || ''
    }
    imageUrl.value = imageData.value.thumbnail
  }
}

// 组件挂载
onMounted(() => {
  fetchPatientDetail()
  initImageData()
})
</script>

<template>
  <div class="image-analysis-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 主要内容 -->
    <div v-else class="page-content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧区域：患者信息 + 报告 -->
        <div class="left-panel">
          <!-- 患者信息表单 -->
          <div class="patient-form">
            <div class="form-header">
              <h3>患者信息</h3>
            </div>

            <el-form v-if="patientDetail" :model="patientDetail" label-width="80px" size="small">
              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="姓名">
                    <el-input v-model="patientDetail.name" placeholder="请输入姓名" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="性别">
                    <el-select v-model="patientDetail.gender" placeholder="请选择性别" style="width: 100%">
                      <el-option
                        v-for="item in genderOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="年龄">
                    <el-input v-model="patientDetail.age" placeholder="请输入年龄" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="病人ID">
                    <el-input v-model="patientDetail.id" placeholder="请输入病人ID" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="病案号">
                    <el-input v-model="patientDetail.caseNumber" placeholder="请输入病案号" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="申请科室">
                    <el-input v-model="patientDetail.department" placeholder="请输入申请科室" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="申请医生">
                    <el-input v-model="patientDetail.doctor" placeholder="请输入申请医生" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="床号">
                    <el-input v-model="patientDetail.bedNumber" placeholder="请输入床号" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="24">
                  <el-form-item label="检查部位">
                    <el-input v-model="patientDetail.examPart" placeholder="请输入检查部位" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 报告区域 -->
          <div class="report-section">
            <div class="report-header">
              <h3>检查报告</h3>
              <el-select
                v-model="selectedTemplate"
                placeholder="选择报告模板"
                style="width: 200px;"
              >
                <el-option
                  v-for="template in reportTemplates"
                  :key="template.value"
                  :label="template.label"
                  :value="template.value"
                />
              </el-select>
            </div>

            <el-input
              v-model="reportContent"
              type="textarea"
              :rows="20"
              :placeholder="isAnalyzing ? 'AI正在生成检查报告...' : '点击右侧自动分析按钮开始AI分析生成报告'"
              class="report-textarea"
              :autosize="false"
            />
          </div>
        </div>

        <!-- 右侧区域容器 -->
        <div class="right-panel">
          <!-- 图像显示区域 -->
          <div class="image-display">
          <div class="image-header">
            <h3>超声图像</h3>
            <el-button
              :icon="DataAnalysis"
              type="primary"
              @click="autoAnalyze"
              :loading="isAnalyzing"
              :disabled="isAnalyzing"
              class="analyze-btn"
            >
              {{ isAnalyzing ? '分析中...' : '自动分析' }}
            </el-button>
          </div>

          <div class="image-container">
            <img
              v-if="imageUrl"
              :src="imageUrl"
              :alt="imageData?.name || '超声图像'"
              class="ultrasound-image"
              @error="handleImageError"
            />
            <div v-else class="no-image">
              <p>暂无图像数据</p>
              <p class="hint">请从超声画布页面选择图像进行分析</p>
            </div>
          </div>

          <div class="image-info" v-if="imageData">
            <p><strong>文件名：</strong>{{ imageData.name }}</p>
            <p><strong>格式：</strong>{{ imageData.format }}</p>
            <p><strong>大小：</strong>{{ imageData.size }}</p>
            <p><strong>上传时间：</strong>{{ imageData.uploadTime }}</p>
          </div>
          </div>

          <!-- 独立的聊天区域 -->
          <div class="chat-panel">
            <div class="chat-header">
              <h3>智能助手</h3>
            </div>

            <div class="chat-messages">
              <div
                v-for="message in chatMessages"
                :key="message.id"
                :class="['message', message.type]"
              >
                <div class="message-content">
                  {{ message.content }}
                </div>
              </div>
            </div>

            <div class="chat-input">
              <el-input
                v-model="chatInput"
                placeholder="输入您的问题"
                @keyup.enter="sendMessage"
                class="input-field"
              />
              <el-button
                type="primary"
                @click="sendMessage"
                class="send-btn"
              >
                发送
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-analysis-page {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color-page);
  overflow: auto;

  .loading-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--el-bg-color);
    margin: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .page-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 24px;
    padding: 20px;
    min-height: calc(100vh - 40px);
    overflow: visible;
    align-items: flex-start;
  }

  .left-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-height: calc(100vh - 80px);
    overflow: visible;
  }

  .patient-form {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    flex-shrink: 0;

    .form-header {
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 16px;
    }

    :deep(.el-form-item__label) {
      font-weight: 500;
      color: var(--el-text-color-regular);
    }
  }

  .report-section {
    flex: 1;
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    min-height: 620px;
    overflow: visible;

    .report-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      flex-shrink: 0;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .report-textarea {
      flex: 1;
      min-height: 520px;

      :deep(.el-textarea) {
        height: 100%;

        .el-textarea__inner {
          height: 520px !important;
          min-height: 520px !important;
          max-height: 520px !important;
          font-family: 'Microsoft YaHei', sans-serif;
          line-height: 1.6;
          font-size: 14px;
          resize: none !important;
          overflow-y: auto;
        }
      }

      // 额外确保禁用拖拉
      :deep(textarea) {
        resize: none !important;
        height: 520px !important;
        min-height: 520px !important;
        max-height: 520px !important;
      }
    }
  }

  .right-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 500px;
    flex-shrink: 0;
  }

  .image-display {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;

    .image-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }

      .analyze-btn {
        font-size: 14px;
      }
    }

    .image-container {
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f8f9fa;
      border-radius: 6px;
      overflow: hidden;
      margin-bottom: 16px;
      height: 300px;
      flex-shrink: 0;

      .ultrasound-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 4px;
      }

      .no-image {
        text-align: center;
        color: var(--el-text-color-placeholder);
        font-size: 14px;

        .hint {
          margin-top: 8px;
          font-size: 12px;
          color: var(--el-text-color-secondary);
        }
      }
    }

    .image-info {
      background: var(--el-bg-color-page);
      padding: 12px;
      border-radius: 6px;
      font-size: 13px;
      flex-shrink: 0;

      p {
        margin: 4px 0;
        color: var(--el-text-color-regular);

        strong {
          color: var(--el-text-color-primary);
        }
      }
    }
  }

  .chat-panel {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: 350px;
    flex-shrink: 0;

    .chat-header {
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid var(--el-border-color-lighter);
      flex-shrink: 0;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .chat-messages {
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 16px;
      min-height: 0;

      .message {
        max-width: 80%;
        word-wrap: break-word;

        &.system {
          align-self: center;

          .message-content {
            background: var(--el-color-info-light-9);
            color: var(--el-color-info);
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 12px;
            text-align: center;
          }
        }

        &.user {
          align-self: flex-end;

          .message-content {
            background: var(--el-color-primary);
            color: white;
            padding: 8px 12px;
            border-radius: 12px 12px 4px 12px;
            font-size: 13px;
          }
        }

        &.assistant {
          align-self: flex-start;

          .message-content {
            background: var(--el-bg-color-page);
            color: var(--el-text-color-primary);
            padding: 8px 12px;
            border-radius: 12px 12px 12px 4px;
            border: 1px solid var(--el-border-color-lighter);
            font-size: 13px;
            line-height: 1.4;
          }
        }
      }
    }

    .chat-input {
      display: flex;
      gap: 8px;
      flex-shrink: 0;

      .input-field {
        flex: 1;
      }

      .send-btn {
        flex-shrink: 0;
        font-size: 13px;
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 1200px) {
  .image-analysis-page {
    .main-content {
      flex-direction: column;
      gap: 16px;
    }

    .left-panel {
      flex-direction: row;
      gap: 16px;
    }

    .patient-form {
      flex: 1;
    }

    .report-section {
      flex: 1;
    }

    .image-display {
      width: 100%;
      height: 400px;
    }
  }
}

@media screen and (max-width: 768px) {
  .image-analysis-page {
    .main-content {
      padding: 16px;
    }

    .left-panel {
      flex-direction: column;
    }

    .patient-form,
    .image-display,
    .report-section {
      padding: 16px;
    }

    .image-display {
      height: 300px;
    }
  }
}
</style>
